<?php

namespace App\Filament\Resources;

use App\Filament\Resources\KeterlambatanResource\Pages;
use App\Models\Absensi;
use App\Models\Karyawan;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\Filter;
use Filament\Forms\Components\DatePicker;

class KeterlambatanResource extends Resource
{
    protected static ?string $model = Absensi::class;

    protected static ?string $navigationIcon = 'heroicon-o-clock';
    
    protected static ?string $navigationGroup = 'Jadwal & Absensi';
    
    protected static ?string $navigationLabel = 'Keterlambatan';
    
    protected static ?string $modelLabel = 'Keterlambatan';
    
    protected static ?string $pluralModelLabel = 'Data Keterlambatan';
    
    protected static ?int $navigationSort = 6;

    public static function canAccess(): bool
    {
        $user = Auth::user();
        return $user->hasRole(['super_admin', 'manager_hrd', 'kepala_toko', 'direktur']) ||
            $user->role === 'supervisor';
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                // Form tidak diperlukan karena ini read-only
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('tanggal_absensi')
                    ->label('Tanggal')
                    ->date('d M Y')
                    ->sortable(),

                Tables\Columns\TextColumn::make('karyawan.nama_lengkap')
                    ->label('Nama Karyawan')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('karyawan.nip')
                    ->label('NIP')
                    ->searchable(),

                Tables\Columns\TextColumn::make('karyawan.entitas.nama')
                    ->label('Entitas')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('karyawan.departemen.nama_departemen')
                    ->label('Departemen')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('jadwal.shift.nama_shift')
                    ->label('Shift')
                    ->searchable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('waktu_masuk')
                    ->label('Waktu Masuk')
                    ->time('H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('jadwal_waktu_masuk')
                    ->label('Jadwal Masuk')
                    ->getStateUsing(function ($record) {
                        if (!$record->jadwal || !$record->jadwal->shift) {
                            return '-';
                        }
                        
                        $shift = $record->jadwal->shift;
                        if ($shift->is_split_shift && $record->periode) {
                            // Handle split shift
                            if (method_exists($shift, 'getWorkPeriods')) {
                                $periods = $shift->getWorkPeriods();
                                foreach ($periods as $period) {
                                    if ($period['periode'] == $record->periode) {
                                        return Carbon::parse($period['waktu_mulai'])->format('H:i');
                                    }
                                }
                            }
                        }
                        
                        return $shift->waktu_mulai ? $shift->waktu_mulai->format('H:i') : '-';
                    }),

                Tables\Columns\TextColumn::make('menit_terlambat')
                    ->label('Menit Terlambat')
                    ->getStateUsing(function ($record) {
                        if (!$record->waktu_masuk || !$record->jadwal) {
                            return 0;
                        }

                        $shift = $record->jadwal->shift;
                        if (!$shift) {
                            return 0;
                        }

                        try {
                            $waktuMasukAktual = Carbon::parse($record->waktu_masuk);

                            // Handle split shift
                            if ($shift->is_split_shift ?? false) {
                                if (method_exists($shift, 'getCurrentPeriod') && method_exists($shift, 'getWorkPeriods')) {
                                    $currentPeriod = $shift->getCurrentPeriod($waktuMasukAktual->format('H:i:s'));
                                    $periods = $shift->getWorkPeriods();

                                    foreach ($periods as $period) {
                                        if ($period['periode'] == $currentPeriod) {
                                            $shiftStart = Carbon::parse($period['waktu_mulai']);
                                            $toleranceMinutes = $period['toleransi_keterlambatan'] ?? 0;
                                            $allowedEntry = $shiftStart->copy()->addMinutes($toleranceMinutes);

                                            if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                                return $waktuMasukAktual->diffInMinutes($allowedEntry);
                                            }
                                            return 0;
                                        }
                                    }
                                }
                            } else {
                                // Regular shift
                                $tanggalWaktuMasuk = $waktuMasukAktual->copy()->startOfDay();
                                $waktuMasukShift = $tanggalWaktuMasuk->copy()->setTimeFromTimeString($shift->waktu_mulai->format('H:i:s'));
                                $toleranceMinutes = $shift->toleransi_keterlambatan ?? 0;
                                $allowedEntry = $waktuMasukShift->copy()->addMinutes($toleranceMinutes);

                                if ($waktuMasukAktual->greaterThan($allowedEntry)) {
                                    return $waktuMasukAktual->diffInMinutes($allowedEntry);
                                }
                            }

                            return 0;
                        } catch (\Exception $e) {
                            return 0;
                        }
                    })
                    ->badge()
                    ->color(fn($state) => $state > 0 ? 'danger' : 'success')
                    ->formatStateUsing(fn($state) => $state > 0 ? $state . ' menit' : 'Tepat waktu')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Status')
                    ->badge()
                    ->color(fn(string $state): string => \App\Enums\AttendanceStatus::from($state)->badgeColor())
                    ->formatStateUsing(fn(string $state): string => \App\Enums\AttendanceStatus::from($state)->label())
                    ->sortable(),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(30)
                    ->toggleable(),
            ])
            ->filters([
                SelectFilter::make('entitas')
                    ->label('Entitas')
                    ->relationship('karyawan.entitas', 'nama')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('departemen')
                    ->label('Departemen')
                    ->relationship('karyawan.departemen', 'nama_departemen')
                    ->searchable()
                    ->preload(),

                SelectFilter::make('karyawan_id')
                    ->label('Karyawan')
                    ->relationship('karyawan', 'nama_lengkap')
                    ->searchable()
                    ->preload(),

                Filter::make('tanggal_range')
                    ->form([
                        DatePicker::make('dari_tanggal')
                            ->label('Dari Tanggal'),
                        DatePicker::make('sampai_tanggal')
                            ->label('Sampai Tanggal'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query
                            ->when(
                                $data['dari_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '>=', $date),
                            )
                            ->when(
                                $data['sampai_tanggal'],
                                fn(Builder $query, $date): Builder => $query->whereDate('tanggal_absensi', '<=', $date),
                            );
                    }),

                Filter::make('hanya_terlambat')
                    ->label('Hanya Yang Terlambat')
                    ->query(fn(Builder $query): Builder => $query->where('status', 'terlambat')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->defaultSort('tanggal_absensi', 'desc')
            ->emptyStateHeading('Tidak Ada Data Keterlambatan')
            ->emptyStateDescription('Belum ada data keterlambatan karyawan.')
            ->emptyStateIcon('heroicon-o-clock');
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()
            ->with([
                'karyawan:id,nama_lengkap,nip,id_entitas,id_departemen',
                'karyawan.entitas:id,nama',
                'karyawan.departemen:id,nama_departemen',
                'jadwal.shift:id,nama_shift,waktu_mulai,waktu_selesai,is_split_shift,toleransi_keterlambatan,periode_1_mulai,periode_1_selesai,periode_2_mulai,periode_2_selesai,toleransi_keterlambatan_periode_1,toleransi_keterlambatan_periode_2'
            ])
            ->whereNotNull('waktu_masuk')
            ->where('status', 'terlambat'); // Hanya tampilkan yang terlambat

        // Filter based on user role
        $user = Auth::user();
        if ($user) {
            if ($user->hasRole(['kepala_toko'])) {
                // Kepala toko can see all requests from their region/entitas
                $query->whereHas('karyawan', function ($q) use ($user) {
                    $q->where('id_entitas', $user->karyawan->id_entitas);
                });
            } elseif ($user->hasAnyRole(['manager_hrd'])) {
                // Manager HRD can see all requests from all entitas - no filtering needed
            } elseif ($user->hasAnyRole(['supervisor'])) {
                // Supervisors can only see their supervised employees' requests
                $query->whereHas('karyawan', function ($q) use ($user) {
                    $q->where('supervisor_id', $user->id);
                });
            }
        }

        return $query;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListKeterlambatans::route('/'),
            'view' => Pages\ViewKeterlambatan::route('/{record}'),
        ];
    }
}
