<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;


class PenggajianRelationManager extends RelationManager
{
    protected static string $relationship = 'penggajian';
    protected static ?string $title = 'Riwayat Penggajian';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\Section::make('Informasi Dasar')
                ->schema([
                    TextInput::make('no_penggajian')
                        ->label('Nomor Penggajian')
                        ->disabled()
                        ->placeholder('Otomatis di-generate'),

                    Forms\Components\Select::make('periode_gaji')
                        ->label('Jenis/Periode Gaji')
                        ->options([
                            'Basis Gaji' => 'Basis Gaji',
                            'Payroll Januari 2025' => 'Payroll Januari 2025',
                            'Payroll Februari 2025' => 'Payroll Februari 2025',
                            'Payroll Maret 2025' => 'Payroll Maret 2025',
                            'Payroll April 2025' => 'Payroll April 2025',
                            'Payroll Mei 2025' => 'Payroll Mei 2025',
                            'Payroll Juni 2025' => 'Payroll Juni 2025',
                        ])
                        ->default('Basis Gaji')
                        ->required()
                        ->searchable(),
                ])->columns(2),

            Forms\Components\Section::make('Komponen Gaji')
                ->schema([
                    TextInput::make('gaji_pokok')
                        ->label('Gaji Pokok')
                        ->numeric()
                        ->required()
                        ->minValue(0)
                        ->prefix('Rp')
                        ->placeholder('5.000.000')
                        ->helperText('Gaji pokok sebelum tunjangan'),

                    TextInput::make('tunjangan_jabatan')
                        ->label('Tunjangan Jabatan')
                        ->numeric()
                        ->default(0)
                        ->minValue(0)
                        ->prefix('Rp')
                        ->placeholder('1.000.000')
                        ->helperText('Tunjangan berdasarkan jabatan'),

                    TextInput::make('tunjangan_umum')
                        ->label('Tunjangan Umum')
                        ->numeric()
                        ->default(0)
                        ->minValue(0)
                        ->prefix('Rp')
                        ->placeholder('500.000')
                        ->helperText('Tunjangan umum/transport'),

                    TextInput::make('tunjangan_sembako')
                        ->label('Tunjangan Sembako')
                        ->numeric()
                        ->default(0)
                        ->minValue(0)
                        ->prefix('Rp')
                        ->placeholder('300.000')
                        ->helperText('Tunjangan sembako/makan'),
                ])->columns(2),

            Forms\Components\Section::make('Potongan BPJS')
                ->schema([
                    TextInput::make('bpjs_kesehatan_dipotong')
                        ->label('BPJS Kesehatan (Dipotong)')
                        ->numeric()
                        ->default(0)
                        ->minValue(0)
                        ->prefix('Rp')
                        ->placeholder('50.000')
                        ->helperText('Potongan BPJS Kesehatan dari gaji'),

                    TextInput::make('bpjs_tk_dipotong')
                        ->label('BPJS Ketenagakerjaan (Dipotong)')
                        ->numeric()
                        ->default(0)
                        ->minValue(0)
                        ->prefix('Rp')
                        ->placeholder('100.000')
                        ->helperText('Potongan BPJS TK dari gaji'),

                    TextInput::make('potongan_lainnya')
                        ->label('Potongan Lainnya')
                        ->numeric()
                        ->default(0)
                        ->minValue(0)
                        ->prefix('Rp')
                        ->placeholder('0')
                        ->helperText('Potongan lain-lain (opsional)'),
                ])->columns(3),

            Forms\Components\Section::make('Keterangan')
                ->schema([
                    Textarea::make('keterangan')
                        ->label('Keterangan')
                        ->rows(3)
                        ->placeholder('Contoh: Basis gaji untuk sistem payroll, Kenaikan gaji, dll.')
                        ->helperText('Keterangan tambahan tentang penggajian ini'),
                ])->columns(1),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('no_penggajian')
                    ->label('No. Penggajian')
                    ->sortable()
                    ->searchable()
                    ->copyable()
                    ->toggleable(),

                Tables\Columns\TextColumn::make('periode_gaji')
                    ->label('Jenis/Periode')
                    ->sortable()
                    ->searchable()
                    ->badge()
                    ->color(fn(string $state): string => match ($state) {
                        'Basis Gaji' => 'gray',
                        default => 'success',
                    }),

                Tables\Columns\TextColumn::make('gaji_pokok')
                    ->label('Gaji Pokok')
                    ->money('IDR')
                    ->sortable(),

                Tables\Columns\TextColumn::make('total_tunjangan')
                    ->label('Total Tunjangan')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->tunjangan_jabatan + $record->tunjangan_umum + $record->tunjangan_sembako)
                    ->sortable(false),

                Tables\Columns\TextColumn::make('total_potongan')
                    ->label('Total Potongan')
                    ->money('IDR')
                    ->getStateUsing(fn($record) => $record->bpjs_kesehatan_dipotong + $record->bpjs_tk_dipotong + $record->potongan_lainnya)
                    ->color('danger')
                    ->sortable(false),

                Tables\Columns\TextColumn::make('take_home_pay')
                    ->label('Take Home Pay')
                    ->money('IDR')
                    ->sortable()
                    ->color('success')
                    ->weight('bold'),

                Tables\Columns\TextColumn::make('keterangan')
                    ->label('Keterangan')
                    ->limit(40)
                    ->tooltip(function ($record) {
                        return $record->keterangan;
                    })
                    ->toggleable(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Dibuat')
                    ->dateTime('d M Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->defaultSort('created_at', 'desc')
            ->actions([
                Tables\Actions\ViewAction::make()
                    ->modalHeading('Detail Penggajian'),
                Tables\Actions\EditAction::make()
                    ->modalHeading('Edit Penggajian'),
                Tables\Actions\DeleteAction::make()
                    ->requiresConfirmation(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()
                    ->modalHeading('Tambah Basis Gaji / Riwayat Gaji')
                    ->modalWidth('4xl'),
            ])
            ->emptyStateHeading('Belum Ada Riwayat Gaji')
            ->emptyStateDescription('Tambahkan basis gaji untuk karyawan ini.')
            ->emptyStateIcon('heroicon-o-banknotes');
    }
}
