<?php

namespace App\Filament\Resources\KaryawanResource\RelationManagers;

use Filament\Forms;
use Filament\Tables;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Filament\Resources\RelationManagers\RelationManager;

class BpjsRelationManager extends RelationManager
{
    protected static string $relationship = 'bpjs';
    protected static ?string $title = 'BPJS & Rekening';

    public function form(Form $form): Form
    {
        return $form->schema([
            Forms\Components\TextInput::make('bpjs_kes')
                ->label('BPJS Kesehatan')
                ->numeric()
                ->minLength(13)
                ->maxLength(13)
                ->helperText('13 digit angka'),
            Forms\Components\TextInput::make('bpjs_tk')
                ->label('BPJS Tenaga Kerja')
                ->numeric()
                ->minLength(11)
                ->maxLength(11)
                ->helperText('11 digit angka'),
            Forms\Components\TextInput::make('npwp')
                ->label('NPWP')
                ->numeric()
                ->minLength(15)
                ->maxLength(16)
                ->helperText('15-16 digit angka'),
            Forms\Components\TextInput::make('rekening')
                ->label('Nomor Rekening')
                ->numeric()
                ->minLength(8)
                ->maxLength(20)
                ->helperText('8-20 digit angka'),
            Forms\Components\Select::make('bank')
                ->label('Bank')
                ->options([
                    'BCA' => 'Bank Central Asia (BCA)',
                    'BRI' => 'Bank Rakyat Indonesia (BRI)',
                    'BNI' => 'Bank Negara Indonesia (BNI)',
                    'Mandiri' => 'Bank Mandiri',
                    'CIMB Niaga' => 'CIMB Niaga',
                    'Danamon' => 'Bank Danamon',
                    'Permata' => 'Bank Permata',
                    'Maybank' => 'Maybank Indonesia',
                    'OCBC NISP' => 'OCBC NISP',
                    'Panin' => 'Bank Panin',
                    'BTN' => 'Bank Tabungan Negara (BTN)',
                    'Mega' => 'Bank Mega',
                    'Bukopin' => 'Bank Bukopin',
                    'BTPN' => 'Bank Tabungan Pensiunan Nasional (BTPN)',
                    'Sinarmas' => 'Bank Sinarmas',
                    'Commonwealth' => 'Commonwealth Bank',
                    'Muamalat' => 'Bank Muamalat',
                    'BSI' => 'Bank Syariah Indonesia (BSI)',
                    'BJB' => 'Bank Jabar Banten (BJB)',
                    'BPD' => 'Bank Pembangunan Daerah',
                    'Lainnya' => 'Bank Lainnya',
                ])
                ->searchable()
                ->nullable(),
        ]);
    }

    public function table(Table $table): Table
    {
        return $table->columns([
            Tables\Columns\TextColumn::make('bpjs_kes'),
            Tables\Columns\TextColumn::make('bpjs_tk'),
            Tables\Columns\TextColumn::make('npwp'),
        ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->visible(fn($livewire) => !$livewire->ownerRecord->bpjs),
            ]);
    }
}
