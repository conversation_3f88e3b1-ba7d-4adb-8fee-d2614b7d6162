name: 🚀 Smart Deploy Website on Push to Dev

on:
  push:
    branches:
      - dev # This workflow triggers on pushes to the dev branch

jobs:
  deploy-development: # Renamed job for clarity
    name: 🎉 Self-Healing Deploy to Development
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: SSH and Deploy
        uses: appleboy/ssh-action@master
        with:
          host: ${{ secrets.HOST }}
          username: ${{ secrets.USERNAME }}
          port: ${{ secrets.PORT }}
          key: ${{ secrets.KEY }}
          passphrase: ${{ secrets.PASSPHRASE }}
          script: |
            # Navigate to the development directory
            cd /home/<USER>/domains/vieraoffice.com/public_html/dev 

            echo "Pulling latest code for development..."
            git pull

            echo "Running Laravel optimizations for development..."
            php artisan migrate --force || echo "Migrations skipped or already up to date."
            php artisan optimize

            echo "Installing npm packages for development..."
            npm install

            echo "Building frontend assets for development..."
            npm run build

            echo "Development deployment completed successfully!"
